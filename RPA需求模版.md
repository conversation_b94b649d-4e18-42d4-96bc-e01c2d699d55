---
文档标题: RPA需求模版
创建日期: 2025-07-29
修订日期: 2025-07-29
版本: v1.0
适用范围: 广州融贸RPA项目组
RPA供应商: 实在RPA（实在智能）
---
# RPA需求申请表

## 1. 基本信息

### 1.1 需求基础信息

| 字段                   | 内容   | 备注（正式文档请删除此列）                                                                      |
| ---------------------- | ------ | ----------------------------------------------------------------------------------------------- |
| **需求编号**     | [必填] | 按照开发规范编写                                                                                |
| **需求标题**     | [必填] | 简洁描述需求内容，控制在20字以内                                                                |
| **提出部门**     | [必填] | 选择：供应链/市场/客服/财务/技术/人事/运营/仓储/采购                                            |
| **需求提出人**   | [必填] | 填写姓名和联系方式                                                                              |
| **提出日期**     | [必填] | YYYY-MM-DD格式                                                                                  |
| **期望完成日期** | [必填] | YYYY-MM-DD格式                                                                                  |
| **业务类型**     | [必填] | 选择：订单处理/库存管理/报表生成/客户管理/财务处理/物流跟踪/产品管理/营销推广/质量管控/数据同步 |

### 1.2 优先级评估

🔴 **必须** - 请根据业务影响程度选择优先级：

- [ ] **L1 - 最高优先级**: 影响核心业务，故障会导致业务中断，需要立即处理
- [ ] **L2 - 中等优先级**: 提升工作效率，故障影响有限，需要及时处理
- [ ] **L3 - 一般优先级**: 辅助功能，故障不影响主要业务，可延后处理

**优先级选择理由**: [必填，说明为什么选择此优先级]

### 1.3 调度需求

🔴 **必须** - 请选择流程执行方式：

**定时执行类型：**

- [ ] 每日一次 - 具体时间：____点____分
- [ ] 每日两次 - 具体时间：____点____分 和 ____点____分
- [ ] 每日四次 - 具体时间：____点、____点、____点、____点
- [ ] 每日六次 - 具体时间：____点、____点、____点、____点、____点、____点
- [ ] 每周一次 - 具体时间：星期____，____点____分
- [ ] 每月一次 - 具体时间：每月____号，____点____分

**固定时点执行：**

- [ ] 每日8点
- [ ] 每日12点
- [ ] 每日18点
- [ ] 每日22点
- [ ] 工作日9点
- [ ] 工作日17点

**其他触发类型：**

- [ ] 手动触发 - 说明触发场景：________________
- [ ] 事件触发 - 说明触发条件：________________
- [ ] 实时监控 - 说明监控对象：________________

## 2. 业务需求描述

### 2.1 当前业务现状

🔴 **必须** - 详细描述当前的业务处理方式：

**当前处理流程表**：

| 步骤序号 | 操作内容         | 使用系统   | 耗时(分钟) | 操作人员 | 存在问题   |
| -------- | ---------------- | ---------- | ---------- | -------- | ---------- |
| 1        | [描述第一步操作] | [系统名称] | [时间]     | [操作人] | [问题描述] |
| 2        | [描述第二步操作] | [系统名称] | [时间]     | [操作人] | [问题描述] |
| 3        | [描述第三步操作] | [系统名称] | [时间]     | [操作人] | [问题描述] |
| 4        | [描述第四步操作] | [系统名称] | [时间]     | [操作人] | [问题描述] |
| ...      | [继续添加步骤]   | [...]      | [...]      | [...]    | [...]      |

**涉及系统平台统计**：

| 系统名称       | 是否涉及      | 使用频率         | 主要功能   | 备注             |
| -------------- | ------------- | ---------------- | ---------- | ---------------- |
| 亚马逊卖家中心 | [ ] 是 [ ] 否 | [每日/每周/每月] | [主要用途] | [特殊说明]       |
| 速卖通卖家中心 | [ ] 是 [ ] 否 | [每日/每周/每月] | [主要用途] | [特殊说明]       |
| Shopee卖家中心 | [ ] 是 [ ] 否 | [每日/每周/每月] | [主要用途] | [特殊说明]       |
| 领星ERP        | [ ] 是 [ ] 否 | [每日/每周/每月] | [主要用途] | [特殊说明]       |
| 易仓ERP        | [ ] 是 [ ] 否 | [每日/每周/每月] | [主要用途] | [特殊说明]       |
| 其他系统       | [ ] 是 [ ] 否 | [每日/每周/每月] | [主要用途] | [系统名称及说明] |

**当前效率统计**：

| 统计项目     | 数值       | 单位 | 备注                   |
| ------------ | ---------- | ---- | ---------------------- |
| 单次处理耗时 | [填写数字] | 分钟 | [说明是否包含等待时间] |
| 每日处理次数 | [填写数字] | 次   | [说明处理频率]         |
| 每日总耗时   | [填写数字] | 小时 | [包含所有相关操作时间] |
| 每月总耗时   | [填写数字] | 小时 | [按工作日计算]         |
| 涉及人员数量 | [填写数字] | 人   | [参与此业务的人员总数] |

### 2.2 自动化需求描述

🔴 **必须** - 明确说明希望RPA实现的功能：

**自动化操作步骤详表**：

| 序号 | 主要步骤   | 操作描述                                               | 需要处理的数据                               | 操作截图                 | 备注                       |
| ---- | ---------- | ------------------------------------------------------ | -------------------------------------------- | ------------------------ | -------------------------- |
| 1    | [步骤名称] | [详细描述具体操作，如：登录系统、点击按钮、输入数据等] | [说明此步骤需要处理什么数据，数据来源和格式] | [可贴截图或说明"见附件"] | [特殊注意事项、异常情况等] |
| 2    | [步骤名称] | [详细描述具体操作]                                     | [说明此步骤需要处理什么数据]                 | [可贴截图或说明"见附件"] | [特殊注意事项、异常情况等] |
| 3    | [步骤名称] | [详细描述具体操作]                                     | [说明此步骤需要处理什么数据]                 | [可贴截图或说明"见附件"] | [特殊注意事项、异常情况等] |
| 4    | [步骤名称] | [详细描述具体操作]                                     | [说明此步骤需要处理什么数据]                 | [可贴截图或说明"见附件"] | [特殊注意事项、异常情况等] |
| 5    | [步骤名称] | [详细描述具体操作]                                     | [说明此步骤需要处理什么数据]                 | [可贴截图或说明"见附件"] | [特殊注意事项、异常情况等] |
| ...  | [继续添加] | [根据实际需要添加更多步骤]                             | [...]                                        | [...]                    | [...]                      |

**整体数据处理说明**：

- **数据来源**: ________________
- **数据格式**: ________________
- **数据量级**: 每次处理约____条数据
- **数据示例**: [请提供1-2个具体的数据示例]

**预期输出结果**：
[请描述RPA执行完成后应该产生什么结果，如：生成报表、更新数据、发送通知等]

## 3. 技术要求

### 3.1 系统环境信息

🔴 **必须** - 提供相关系统的详细信息：

**涉及的网站/系统**：

| 系统名称 | 网址/路径 | 登录方式           | 权限要求       |
| -------- | --------- | ------------------ | -------------- |
| [系统1]  | [网址]    | [用户名密码/SSO等] | [需要什么权限] |
| [系统2]  | [网址]    | [用户名密码/SSO等] | [需要什么权限] |

**文件处理要求**：

| 文件类型 | 格式要求          | 存放位置   | 命名规则   | 备注           |
| -------- | ----------------- | ---------- | ---------- | -------------- |
| 输入文件 | [Excel/CSV/TXT等] | [具体路径] | [命名格式] | [特殊要求]     |
| 输出文件 | [Excel/CSV/TXT等] | [具体路径] | [命名格式] | [特殊要求]     |
| 临时文件 | [Excel/CSV/TXT等] | [具体路径] | [命名格式] | [是否需要保留] |
| 备份文件 | [Excel/CSV/TXT等] | [具体路径] | [命名格式] | [保留时间]     |

### 3.2 异常处理要求

🟡 **建议** - 考虑可能出现的异常情况：

**异常情况处理表**：

| 异常类型     | 可能性               | 处理方式         | 重试次数 | 通知方式             | 备注               |
| ------------ | -------------------- | ---------------- | -------- | -------------------- | ------------------ |
| 网络连接失败 | [ ] 高 [ ] 中 [ ] 低 | [重试/跳过/停止] | [次数]   | [邮件/短信/系统通知] | [特殊说明]         |
| 登录失败     | [ ] 高 [ ] 中 [ ] 低 | [重试/跳过/停止] | [次数]   | [邮件/短信/系统通知] | [特殊说明]         |
| 数据格式错误 | [ ] 高 [ ] 中 [ ] 低 | [重试/跳过/停止] | [次数]   | [邮件/短信/系统通知] | [特殊说明]         |
| 文件不存在   | [ ] 高 [ ] 中 [ ] 低 | [重试/跳过/停止] | [次数]   | [邮件/短信/系统通知] | [特殊说明]         |
| 系统维护中   | [ ] 高 [ ] 中 [ ] 低 | [重试/跳过/停止] | [次数]   | [邮件/短信/系统通知] | [特殊说明]         |
| 其他异常     | [ ] 高 [ ] 中 [ ] 低 | [重试/跳过/停止] | [次数]   | [邮件/短信/系统通知] | [具体说明异常类型] |

## 4. 预期效果

### 4.1 效率提升预期

🔴 **必须** - 量化描述预期效果：

**效率提升对比表**：

| 对比项目     | 当前人工处理 | 自动化后预期 | 提升幅度       | 计算说明       |
| ------------ | ------------ | ------------ | -------------- | -------------- |
| 单次处理时间 | [数字]分钟   | [数字]分钟   | 节省[数字]分钟 | [说明计算方式] |
| 每日处理次数 | [数字]次     | [数字]次     | 增加[数字]次   | [说明频率变化] |
| 每日总耗时   | [数字]小时   | [数字]小时   | 节省[数字]小时 | [包含所有环节] |
| 每月总耗时   | [数字]小时   | [数字]小时   | 节省[数字]小时 | [按工作日计算] |
| 人工错误率   | [数字]%      | [数字]%      | 降低[数字]%    | [错误类型说明] |
| 数据及时性   | [延迟时间]   | [延迟时间]   | 提升[时间]     | [及时性改善]   |

**其他效益评估**：

| 效益类型 | 具体描述                 | 量化指标      | 预期效果   |
| -------- | ------------------------ | ------------- | ---------- |
| 人力释放 | [释放人员可做其他工作]   | [释放人天/月] | [具体效果] |
| 数据质量 | [数据准确性、完整性提升] | [质量指标]    | [改善程度] |
| 业务响应 | [处理速度、响应时间改善] | [时间指标]    | [改善效果] |
| 成本节约 | [人力成本、时间成本节约] | [金额/时间]   | [节约数额] |

### 4.2 成功标准

🔴 **必须** - 明确定义什么情况下认为RPA开发成功：

**功能完整性标准**：

- [ ] 能够完成所有指定的自动化操作
- [ ] 处理结果准确率达到____%以上
- [ ] 执行时间控制在____分钟以内

**稳定性标准**：

- [ ] 连续运行____天无故障
- [ ] 异常情况能够正确处理
- [ ] 日志记录完整清晰

## 5. 风险评估

### 5.1 业务风险

🟡 **建议** - 评估可能的业务风险：

**数据安全风险**：
[评估是否涉及敏感数据，如何保护]

**业务连续性风险**：
[评估RPA故障对业务的影响程度]

**合规风险**：
[评估是否符合相关法规和公司政策]

### 5.2 技术风险评估

🟡 **建议** - 评估技术实现风险：

**技术风险评估表**：

| 风险类型   | 风险等级             | 风险描述           | 影响程度           | 应对措施         |
| ---------- | -------------------- | ------------------ | ------------------ | ---------------- |
| 技术复杂度 | [ ] 高 [ ] 中 [ ] 低 | [具体技术难点]     | [对项目的影响]     | [建议的解决方案] |
| 系统依赖性 | [ ] 高 [ ] 中 [ ] 低 | [依赖的外部系统]   | [系统不稳定的影响] | [降低依赖的方法] |
| 数据安全   | [ ] 高 [ ] 中 [ ] 低 | [涉及的敏感数据]   | [泄露风险]         | [安全保护措施]   |
| 兼容性     | [ ] 高 [ ] 中 [ ] 低 | [系统版本兼容问题] | [升级影响]         | [兼容性解决方案] |
| 性能风险   | [ ] 高 [ ] 中 [ ] 低 | [性能瓶颈点]       | [对业务的影响]     | [性能优化方案]   |

## 6. 资源需求

### 6.1 人力资源需求

**资源需求表**：

| 角色类型 | 人员数量 | 投入时间   | 主要职责             | 技能要求       | 备注           |
| -------- | -------- | ---------- | -------------------- | -------------- | -------------- |
| 业务专家 | [数字]人 | [数字]人天 | [需求澄清、业务指导] | [业务熟练度]   | [配合时间安排] |
| 测试人员 | [数字]人 | [数字]人天 | [功能测试、验收测试] | [测试经验]     | [测试环境要求] |
| 培训对象 | [数字]人 | [数字]小时 | [学习使用RPA流程]    | [学习能力]     | [培训方式]     |
| RPA开发  | [数字]人 | [数字]人天 | [流程开发、调试]     | [RPA开发经验]  | [预估工作量]   |
| 项目管理 | [数字]人 | [数字]人天 | [项目协调、进度管理] | [项目管理经验] | [管理范围]     |

### 6.2 其他资源需求

**系统资源需求表**：

| 资源类型 | 是否需要      | 具体要求       | 申请流程   | 预计时间   | 负责人   |
| -------- | ------------- | -------------- | ---------- | ---------- | -------- |
| 系统账号 | [ ] 是 [ ] 否 | [账号权限要求] | [申请步骤] | [申请周期] | [联系人] |
| 特殊权限 | [ ] 是 [ ] 否 | [权限详细说明] | [申请步骤] | [申请周期] | [联系人] |
| 网络配置 | [ ] 是 [ ] 否 | [网络要求]     | [申请步骤] | [申请周期] | [联系人] |
| 硬件设备 | [ ] 是 [ ] 否 | [设备规格要求] | [申请步骤] | [申请周期] | [联系人] |
| 软件许可 | [ ] 是 [ ] 否 | [软件版本要求] | [申请步骤] | [申请周期] | [联系人] |

## 7. 验收标准

### 7.1 功能验收标准

🔴 **必须** - 明确的验收标准：

**功能验收测试用例表**：

| 测试编号 | 测试场景   | 输入数据   | 预期输出   | 验收标准   | 测试人员     |
| -------- | ---------- | ---------- | ---------- | ---------- | ------------ |
| TC001    | [场景描述] | [具体输入] | [预期结果] | [通过标准] | [测试负责人] |
| TC002    | [场景描述] | [具体输入] | [预期结果] | [通过标准] | [测试负责人] |
| TC003    | [场景描述] | [具体输入] | [预期结果] | [通过标准] | [测试负责人] |
| TC004    | [场景描述] | [具体输入] | [预期结果] | [通过标准] | [测试负责人] |
| TC005    | [场景描述] | [具体输入] | [预期结果] | [通过标准] | [测试负责人] |

### 7.2 性能验收标准

**性能指标要求表**：

| 性能指标  | 目标值                     | 测试方法   | 测试环境   | 验收标准   |
| --------- | -------------------------- | ---------- | ---------- | ---------- |
| 处理速度  | 每分钟处理[数字]条数据     | [测试方式] | [环境要求] | [达标标准] |
| 响应时间  | 操作响应时间不超过[数字]秒 | [测试方式] | [环境要求] | [达标标准] |
| CPU使用率 | 不超过[数字]%              | [监控工具] | [环境要求] | [达标标准] |
| 内存使用  | 不超过[数字]MB             | [监控工具] | [环境要求] | [达标标准] |
| 成功率    | 不低于[数字]%              | [统计方法] | [环境要求] | [达标标准] |
| 稳定性    | 连续运行[数字]小时无故障   | [测试方法] | [环境要求] | [达标标准] |

## 8. 附件材料

### 8.1 相关文档清单

🟢 **推荐** - 提供以下材料有助于需求理解：

**附件材料清单表**：

| 材料类型     | 文件名称 | 备注说明         |
| ------------ | -------- | ---------------- |
| 业务流程图   | [文件名] | [内容说明]       |
| 系统操作截图 | [文件名] | [截图内容]       |
| 数据样例文件 | [文件名] | [数据说明]       |
| 相关制度文档 | [文件名] | [制度内容]       |
| 操作录屏视频 | [文件名] | [录屏内容]       |
| 其他材料     | [文件名] | [材料说明]------ |

## 备注：正式文档请删除以下内容

## 📋 模版使用说明

**重要提示**: 以下使用说明仅供参考，填写完成后可删除此部分内容。

### 模版适用范围

本模版适用于广州融贸所有部门提交RPA自动化需求。请按照模版要求详细填写各项信息，确保需求描述清晰、完整。

### 🔴 **必须** - 填写要求

- 所有标注为"必填"的字段必须完整填写
- 需求描述必须具体明确，避免模糊表述
- 涉及的系统和操作步骤必须详细说明
- 预期效果必须量化描述

### 🟡 **建议** - 质量要求

- 建议提供相关截图或操作录屏
- 建议明确业务场景和使用频率
- 建议考虑异常情况的处理方式

### 填写技巧

1. **需求标题**: 简洁明了，一句话说清楚要做什么
2. **优先级选择**: 根据业务影响程度客观评估，不要盲目选择高优先级
3. **调度时间**: 考虑业务实际需要和系统负载，选择合适的执行时间
4. **操作步骤表格**:
   - **主要步骤**: 用简短的名词概括，如"登录系统"、"下载数据"、"生成报表"
   - **操作描述**: 详细描述具体操作，包括点击什么按钮、输入什么内容、选择什么选项
   - **需要处理的数据**: 说明此步骤涉及的数据类型、来源、格式
   - **操作截图**: 可以直接粘贴截图，或注明"见附件"
   - **备注**: 记录特殊注意事项、可能的异常情况、业务规则等
5. **数据示例**: 提供真实的数据样例，有助于开发人员理解需求
6. **验收标准**: 设定明确可测试的标准，避免后期争议

### 常见问题

**Q: 不知道如何评估优先级？**
A: L1-影响核心业务运转；L2-提高工作效率；L3-锦上添花的功能

**Q: 调度时间如何选择？**
A: 考虑数据更新时间、业务处理时间、系统负载等因素

**Q: 技术要求不懂怎么填？**
A: 重点描述业务需求，技术细节可以在需求澄清阶段与开发人员沟通

**Q: 验收标准如何设定？**
A: 从功能完整性、准确性、稳定性三个维度设定可量化的标准

**Q: 操作步骤表格如何填写？**
A: 按照实际操作顺序逐步填写，每一行代表一个主要操作步骤。建议先在纸上或脑海中梳理完整流程，再填入表格

**Q: 操作截图如何处理？**
A: 可以直接复制粘贴到表格中，或者将截图保存为文件并在表格中注明"见附件图1"等

---

*本模版由广州融贸RPA项目管理小组制定，版本v1.0，最后更新时间：2025-07-29*
*如有疑问请联系RPA项目经理或通过内部协作平台咨询*
